// Select2 initialization for contact forms
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 on all select elements in contact forms
    const contactFormSelects = document.querySelectorAll('.wpcf7-form select.wpcf7-select');
    
    contactFormSelects.forEach(function(select) {
        if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
            jQuery(select).select2({
                minimumResultsForSearch: Infinity, // Disable search for simple dropdowns
                width: '100%',
                placeholder: select.getAttribute('data-placeholder') || 'Select an option...',
                allowClear: false
            });
        }
    });
    
    // Re-initialize Select2 after AJAX form submissions (Contact Form 7)
    document.addEventListener('wpcf7mailsent', function(event) {
        setTimeout(function() {
            const formSelects = event.detail.contactForm.querySelectorAll('select.wpcf7-select');
            formSelects.forEach(function(select) {
                if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
                    jQuery(select).select2('destroy').select2({
                        minimumResultsForSearch: Infinity,
                        width: '100%',
                        placeholder: select.getAttribute('data-placeholder') || 'Select an option...',
                        allowClear: false
                    });
                }
            });
        }, 100);
    });
    
    // Re-initialize after form validation errors
    document.addEventListener('wpcf7invalid', function(event) {
        setTimeout(function() {
            const formSelects = event.detail.contactForm.querySelectorAll('select.wpcf7-select');
            formSelects.forEach(function(select) {
                if (typeof jQuery !== 'undefined' && typeof jQuery.fn.select2 !== 'undefined') {
                    if (!jQuery(select).hasClass('select2-hidden-accessible')) {
                        jQuery(select).select2({
                            minimumResultsForSearch: Infinity,
                            width: '100%',
                            placeholder: select.getAttribute('data-placeholder') || 'Select an option...',
                            allowClear: false
                        });
                    }
                }
            });
        }, 100);
    });
});
